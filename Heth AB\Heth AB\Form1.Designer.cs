﻿namespace Heth_AB
{
    partial class Form1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.cmbProcesses = new System.Windows.Forms.ComboBox();
            this.btnRefreshProcesses = new System.Windows.Forms.Button();
            this.btnInjectDLL = new System.Windows.Forms.Button();
            this.lblPlayerX = new System.Windows.Forms.Label();
            this.lblPlayerY = new System.Windows.Forms.Label();
            this.lblXValue = new System.Windows.Forms.Label();
            this.lblYValue = new System.Windows.Forms.Label();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.lblStatus = new System.Windows.Forms.Label();
            this.btnStartMonsterScan = new System.Windows.Forms.Button();
            this.btnStopMonsterScan = new System.Windows.Forms.Button();
            this.lstMonsters = new System.Windows.Forms.ListBox();
            this.lblMonsters = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // cmbProcesses
            // 
            this.cmbProcesses.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbProcesses.FormattingEnabled = true;
            this.cmbProcesses.Location = new System.Drawing.Point(12, 12);
            this.cmbProcesses.Name = "cmbProcesses";
            this.cmbProcesses.Size = new System.Drawing.Size(300, 21);
            this.cmbProcesses.TabIndex = 0;
            // 
            // btnRefreshProcesses
            // 
            this.btnRefreshProcesses.Location = new System.Drawing.Point(318, 10);
            this.btnRefreshProcesses.Name = "btnRefreshProcesses";
            this.btnRefreshProcesses.Size = new System.Drawing.Size(75, 23);
            this.btnRefreshProcesses.TabIndex = 1;
            this.btnRefreshProcesses.Text = "Refresh";
            this.btnRefreshProcesses.UseVisualStyleBackColor = true;
            this.btnRefreshProcesses.Click += new System.EventHandler(this.btnRefreshProcesses_Click);
            // 
            // btnInjectDLL
            // 
            this.btnInjectDLL.Location = new System.Drawing.Point(399, 10);
            this.btnInjectDLL.Name = "btnInjectDLL";
            this.btnInjectDLL.Size = new System.Drawing.Size(75, 23);
            this.btnInjectDLL.TabIndex = 2;
            this.btnInjectDLL.Text = "Inject DLL";
            this.btnInjectDLL.UseVisualStyleBackColor = true;
            this.btnInjectDLL.Click += new System.EventHandler(this.btnInjectDLL_Click);
            // 
            // lblPlayerX
            // 
            this.lblPlayerX.AutoSize = true;
            this.lblPlayerX.Location = new System.Drawing.Point(12, 90);
            this.lblPlayerX.Name = "lblPlayerX";
            this.lblPlayerX.Size = new System.Drawing.Size(49, 13);
            this.lblPlayerX.TabIndex = 3;
            this.lblPlayerX.Text = "Player X:";
            // 
            // lblPlayerY
            // 
            this.lblPlayerY.AutoSize = true;
            this.lblPlayerY.Location = new System.Drawing.Point(12, 115);
            this.lblPlayerY.Name = "lblPlayerY";
            this.lblPlayerY.Size = new System.Drawing.Size(49, 13);
            this.lblPlayerY.TabIndex = 4;
            this.lblPlayerY.Text = "Player Y:";
            // 
            // lblXValue
            // 
            this.lblXValue.AutoSize = true;
            this.lblXValue.Location = new System.Drawing.Point(69, 90);
            this.lblXValue.Name = "lblXValue";
            this.lblXValue.Size = new System.Drawing.Size(13, 13);
            this.lblXValue.TabIndex = 5;
            this.lblXValue.Text = "0";
            // 
            // lblYValue
            // 
            this.lblYValue.AutoSize = true;
            this.lblYValue.Location = new System.Drawing.Point(69, 115);
            this.lblYValue.Name = "lblYValue";
            this.lblYValue.Size = new System.Drawing.Size(13, 13);
            this.lblYValue.TabIndex = 6;
            this.lblYValue.Text = "0";
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // lblStatus
            // 
            this.lblStatus.AutoSize = true;
            this.lblStatus.Location = new System.Drawing.Point(12, 36);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(38, 13);
            this.lblStatus.TabIndex = 7;
            this.lblStatus.Text = "Ready";
            // 
            // btnStartMonsterScan
            // 
            this.btnStartMonsterScan.Location = new System.Drawing.Point(480, 10);
            this.btnStartMonsterScan.Name = "btnStartMonsterScan";
            this.btnStartMonsterScan.Size = new System.Drawing.Size(90, 23);
            this.btnStartMonsterScan.TabIndex = 8;
            this.btnStartMonsterScan.Text = "Start Scan";
            this.btnStartMonsterScan.UseVisualStyleBackColor = true;
            this.btnStartMonsterScan.Click += new System.EventHandler(this.btnStartMonsterScan_Click);
            // 
            // btnStopMonsterScan
            // 
            this.btnStopMonsterScan.Enabled = false;
            this.btnStopMonsterScan.Location = new System.Drawing.Point(576, 10);
            this.btnStopMonsterScan.Name = "btnStopMonsterScan";
            this.btnStopMonsterScan.Size = new System.Drawing.Size(90, 23);
            this.btnStopMonsterScan.TabIndex = 9;
            this.btnStopMonsterScan.Text = "Stop Scan";
            this.btnStopMonsterScan.UseVisualStyleBackColor = true;
            this.btnStopMonsterScan.Click += new System.EventHandler(this.btnStopMonsterScan_Click);
            // 
            // lstMonsters
            // 
            this.lstMonsters.FormattingEnabled = true;
            this.lstMonsters.Location = new System.Drawing.Point(202, 74);
            this.lstMonsters.Name = "lstMonsters";
            this.lstMonsters.Size = new System.Drawing.Size(466, 160);
            this.lstMonsters.TabIndex = 10;
            // 
            // lblMonsters
            // 
            this.lblMonsters.AutoSize = true;
            this.lblMonsters.Location = new System.Drawing.Point(202, 58);
            this.lblMonsters.Name = "lblMonsters";
            this.lblMonsters.Size = new System.Drawing.Size(53, 13);
            this.lblMonsters.TabIndex = 11;
            this.lblMonsters.Text = "Monsters:";
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(680, 240);
            this.Controls.Add(this.lblMonsters);
            this.Controls.Add(this.lstMonsters);
            this.Controls.Add(this.btnStopMonsterScan);
            this.Controls.Add(this.btnStartMonsterScan);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.lblYValue);
            this.Controls.Add(this.lblXValue);
            this.Controls.Add(this.lblPlayerY);
            this.Controls.Add(this.lblPlayerX);
            this.Controls.Add(this.btnInjectDLL);
            this.Controls.Add(this.btnRefreshProcesses);
            this.Controls.Add(this.cmbProcesses);
            this.Name = "Form1";
            this.Text = "Heth AB - Engine Memory Reader";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.Form1_FormClosing);
            this.Load += new System.EventHandler(this.Form1_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ComboBox cmbProcesses;
        private System.Windows.Forms.Button btnRefreshProcesses;
        private System.Windows.Forms.Button btnInjectDLL;
        private System.Windows.Forms.Label lblPlayerX;
        private System.Windows.Forms.Label lblPlayerY;
        private System.Windows.Forms.Label lblXValue;
        private System.Windows.Forms.Label lblYValue;
        private System.Windows.Forms.Timer timer1;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.Button btnStartMonsterScan;
        private System.Windows.Forms.Button btnStopMonsterScan;
        private System.Windows.Forms.ListBox lstMonsters;
        private System.Windows.Forms.Label lblMonsters;
    }
}

