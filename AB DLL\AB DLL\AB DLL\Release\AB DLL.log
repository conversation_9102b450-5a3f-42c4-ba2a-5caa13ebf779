﻿  pch.cpp
  dllmain.cpp
  MemoryReader.cpp
     Creating library G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\Release\AB DLL.lib and object G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\Release\AB DLL.exp
  Generating code
  Previous IPDB not found, fall back to full compilation.
  All 10 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finished generating code
  AB DLL.vcxproj -> G:\Kal\Kal Hacks\Heth AB + AB DLL\HethAB\AB DLL\AB DLL\Release\AB DLL.dll
