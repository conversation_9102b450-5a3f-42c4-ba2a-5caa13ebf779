#pragma once
#include "pch.h"
#include <windows.h>
#pragma comment(lib,"detours.lib")
#include "Detours/detours.h"

// std2::map structure definition (simplified version matching game's implementation)
namespace std2 {
    template<typename K, typename V>
    class map {
    public:
        struct Node {
            Node* left;
            Node* parent;
            Node* right;
            char color;
            char pad[3];
            K key;
            V value;
        };

        Node* root;
        Node* nil;
        size_t size_val;

        // Iterator class
        class iterator {
        public:
            Node* node;
            Node* nil_node;

            iterator(Node* n, Node* nil) : node(n), nil_node(nil) {}

            bool operator!=(const iterator& other) const {
                return node != other.node;
            }

            iterator& operator++() {
                if (node->right != nil_node) {
                    node = node->right;
                    while (node->left != nil_node) {
                        node = node->left;
                    }
                } else {
                    Node* parent = node->parent;
                    while (parent != nil_node && node == parent->right) {
                        node = parent;
                        parent = parent->parent;
                    }
                    node = parent;
                }
                return *this;
            }

            K& first() const { return node->key; }
            V& second() const { return node->value; }
        };

        size_t size() const { return size_val; }

        iterator begin() {
            if (root == nil) return iterator(nil, nil);
            Node* leftmost = root;
            while (leftmost->left != nil) {
                leftmost = leftmost->left;
            }
            return iterator(leftmost, nil);
        }

        iterator end() {
            return iterator(nil, nil);
        }
    };
}


// Export macros for C# interop
#ifdef ABDLL_EXPORTS
#define ABDLL_API __declspec(dllexport)
#else
#define ABDLL_API __declspec(dllimport)
#endif

// Game memory addresses namespace
namespace Engine
{
    namespace CGame_Character
    {
        static uintptr_t* m_Master = (uintptr_t*)0x00906824;
    }
}

// Monster scanning
const DWORD EntityIDs = 0x906854;

class TargetInfo
{
public:
    TargetInfo() : m_pTarget(nullptr) {}
    ~TargetInfo() {}
    void* m_pTarget; // opaque pointer to target
};

// Global variables for monster scanning
extern bool g_bMonsterScanningActive;
extern HANDLE g_hMonsterScanThread;

// Monster information structure
struct MonsterInfo
{
    int id;
    int address;
};

// Shared memory structure for communication with C# app
struct PlayerData
{
    int x;
    int y;
    bool isValid;
    int monsterCount;
    MonsterInfo monsters[50]; // Support up to 50 monsters
};

// Global variables
extern HANDLE g_hSharedMemory;
extern PlayerData* g_pSharedData;
extern HANDLE g_hUpdateThread;
extern bool g_bRunning;

// Function declarations for C# interop
extern "C" {
    ABDLL_API bool InitializeMemoryReader();
    ABDLL_API void CleanupMemoryReader();
    ABDLL_API bool GetPlayerCoordinates(int* x, int* y);
    ABDLL_API bool StartContinuousReading();
    ABDLL_API void StopContinuousReading();
    ABDLL_API bool StartMonsterScanning();
    ABDLL_API void StopMonsterScanning();
}

// Internal functions
bool CreateSharedMemory();
void CleanupSharedMemory();
DWORD WINAPI UpdateThread(LPVOID lpParam);
bool ReadPlayerData();
