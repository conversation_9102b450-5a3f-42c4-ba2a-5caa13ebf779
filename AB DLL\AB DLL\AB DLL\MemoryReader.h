#pragma once
#include "pch.h"
#include <windows.h>
#pragma comment(lib,"detours.lib")
#include "Detours/detours.h"


// Export macros for C# interop
#ifdef ABDLL_EXPORTS
#define ABDLL_API __declspec(dllexport)
#else
#define ABDLL_API __declspec(dllimport)
#endif

// Game memory addresses namespace
namespace Engine
{
    namespace CGame_Character
    {
        static uintptr_t* m_Master = (uintptr_t*)0x00906824;
    }
}

// Monster scanning
const DWORD EntityIDs = 0x906854;

class TargetInfo
{
public:
    TargetInfo() : m_pTarget(nullptr) {}
    ~TargetInfo() {}
    void* m_pTarget; // opaque pointer to target
};

// Global variables for monster scanning
extern bool g_bMonsterScanningActive;
extern HANDLE g_hMonsterScanThread;

// Monster information structure
struct MonsterInfo
{
    int id;
    int address;
};

// Shared memory structure for communication with C# app
struct PlayerData
{
    int x;
    int y;
    bool isValid;
    int monsterCount;
    MonsterInfo monsters[50]; // Support up to 50 monsters
};

// Global variables
extern HANDLE g_hSharedMemory;
extern PlayerData* g_pSharedData;
extern HANDLE g_hUpdateThread;
extern bool g_bRunning;

// Function declarations for C# interop
extern "C" {
    ABDLL_API bool InitializeMemoryReader();
    ABDLL_API void CleanupMemoryReader();
    ABDLL_API bool GetPlayerCoordinates(int* x, int* y);
    ABDLL_API bool StartContinuousReading();
    ABDLL_API void StopContinuousReading();
    ABDLL_API bool StartMonsterScanning();
    ABDLL_API void StopMonsterScanning();
}

// Internal functions
bool CreateSharedMemory();
void CleanupSharedMemory();
DWORD WINAPI UpdateThread(LPVOID lpParam);
bool ReadPlayerData();
