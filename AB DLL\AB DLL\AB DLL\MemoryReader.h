#pragma once
#include "pch.h"
#include <windows.h>
#pragma comment(lib,"detours.lib")
#include "Detours/detours.h"

// Export macros for C# interop
#ifdef ABDLL_EXPORTS
#define ABDLL_API __declspec(dllexport)
#else
#define ABDLL_API __declspec(dllimport)
#endif

// Game memory addresses namespace
namespace Engine
{
    namespace CGame_Character
    {
        static uintptr_t* m_Master = (uintptr_t*)0x00906824;
    }
}

// Shared memory structure for communication with C# app
struct PlayerData
{
    int x;
    int y;
    bool isValid;
    int debugCode; // Debug information: 0=success, 1=master null, 2=master zero, 3=exception
};

// Global variables
extern HANDLE g_hSharedMemory;
extern PlayerData* g_pSharedData;
extern HANDLE g_hUpdateThread;
extern bool g_bRunning;

// Function declarations for C# interop
extern "C" {
    ABDLL_API bool InitializeMemoryReader();
    ABDLL_API void CleanupMemoryReader();
    ABDLL_API bool GetPlayerCoordinates(int* x, int* y);
    ABDLL_API bool StartContinuousReading();
    ABDLL_API void StopContinuousReading();
    ABDLL_API bool IsMemoryReaderActive();
    ABDLL_API bool TestMemoryAccess(uintptr_t* masterPtr, uintptr_t* masterValue);
}

// Internal functions
bool CreateSharedMemory();
void CleanupSharedMemory();
DWORD WINAPI UpdateThread(LPVOID lpParam);
bool ReadPlayerData();
