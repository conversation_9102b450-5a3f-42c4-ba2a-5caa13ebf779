#include "pch.h"
#include "MemoryReader.h"
#include <iostream>

// Global variables
HANDLE g_hSharedMemory = nullptr;
PlayerData* g_pSharedData = nullptr;
HANDLE g_hUpdateThread = nullptr;
bool g_bRunning = false;

// Shared memory name for communication with C# app
const wchar_t* SHARED_MEMORY_NAME = L"HethAB_PlayerData";

bool CreateSharedMemory()
{
    // Create shared memory for communication with C# app
    g_hSharedMemory = CreateFileMapping(
        INVALID_HANDLE_VALUE,
        nullptr,
        PAGE_READWRITE,
        0,
        sizeof(PlayerData),
        SHARED_MEMORY_NAME
    );

    if (g_hSharedMemory == nullptr)
    {
        return false;
    }

    g_pSharedData = (PlayerData*)MapViewOfFile(
        g_hSharedMemory,
        FILE_MAP_ALL_ACCESS,
        0,
        0,
        sizeof(PlayerData)
    );

    if (g_pSharedData == nullptr)
    {
        CloseHandle(g_hSharedMemory);
        g_hSharedMemory = nullptr;
        return false;
    }

    // Initialize shared data
    g_pSharedData->x = 0;
    g_pSharedData->y = 0;
    g_pSharedData->isValid = false;
    g_pSharedData->debugCode = 0;

    return true;
}

void CleanupSharedMemory()
{
    if (g_pSharedData != nullptr)
    {
        UnmapViewOfFile(g_pSharedData);
        g_pSharedData = nullptr;
    }

    if (g_hSharedMemory != nullptr)
    {
        CloseHandle(g_hSharedMemory);
        g_hSharedMemory = nullptr;
    }
}

bool ReadPlayerData()
{
    __try
    {
        // Check if master pointer is valid
        if (Engine::CGame_Character::m_Master == nullptr)
        {
            if (g_pSharedData != nullptr)
            {
                g_pSharedData->isValid = false;
                g_pSharedData->debugCode = 1; // Master pointer is null
            }
            return false;
        }

        // Read the master pointer value
        uintptr_t masterValue = *Engine::CGame_Character::m_Master;
        if (masterValue == 0)
        {
            if (g_pSharedData != nullptr)
            {
                g_pSharedData->isValid = false;
                g_pSharedData->debugCode = 2; // Master value is zero
            }
            return false;
        }

        // Read player coordinates
        int x = *(int*)(masterValue + 18236);
        int y = *(int*)(masterValue + 18244);

        // Update shared memory if available
        if (g_pSharedData != nullptr)
        {
            g_pSharedData->x = x;
            g_pSharedData->y = y;
            g_pSharedData->isValid = true;
            g_pSharedData->debugCode = 0; // Success
        }

        return true;
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        // Memory access failed
        if (g_pSharedData != nullptr)
        {
            g_pSharedData->isValid = false;
            g_pSharedData->debugCode = 3; // Exception occurred
        }
        return false;
    }
}

DWORD WINAPI UpdateThread(LPVOID lpParam)
{
    while (g_bRunning)
    {
        ReadPlayerData();
        Sleep(100); // Update every 100ms
    }
    return 0;
}

// Exported functions for C# interop
extern "C" {

    ABDLL_API bool InitializeMemoryReader()
    {
        return CreateSharedMemory();
    }

    ABDLL_API void CleanupMemoryReader()
    {
        StopContinuousReading();
        CleanupSharedMemory();
    }

    ABDLL_API bool GetPlayerCoordinates(int* x, int* y)
    {
        if (x == nullptr || y == nullptr)
            return false;

        __try
        {
            // Check if master pointer is valid
            if (Engine::CGame_Character::m_Master == nullptr)
                return false;

            // Read the master pointer value
            uintptr_t masterValue = *Engine::CGame_Character::m_Master;
            if (masterValue == 0)
                return false;

            // Read player coordinates
            *x = *(int*)(masterValue + 18236);
            *y = *(int*)(masterValue + 18244);

            return true;
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            return false;
        }
    }

    ABDLL_API bool StartContinuousReading()
    {
        if (g_bRunning)
            return true; // Already running

        g_bRunning = true;
        g_hUpdateThread = CreateThread(nullptr, 0, UpdateThread, nullptr, 0, nullptr);
        return g_hUpdateThread != nullptr;
    }

    ABDLL_API void StopContinuousReading()
    {
        g_bRunning = false;
        if (g_hUpdateThread != nullptr)
        {
            WaitForSingleObject(g_hUpdateThread, 5000); // Wait up to 5 seconds
            CloseHandle(g_hUpdateThread);
            g_hUpdateThread = nullptr;
        }
    }

    ABDLL_API bool IsMemoryReaderActive()
    {
        return g_bRunning && (g_hUpdateThread != nullptr) && (g_pSharedData != nullptr);
    }

    ABDLL_API bool TestMemoryAccess(uintptr_t* masterPtr, uintptr_t* masterValue)
    {
        __try
        {
            if (masterPtr != nullptr)
                *masterPtr = (uintptr_t)Engine::CGame_Character::m_Master;

            if (masterValue != nullptr)
            {
                if (Engine::CGame_Character::m_Master != nullptr)
                    *masterValue = *Engine::CGame_Character::m_Master;
                else
                    *masterValue = 0;
            }
            return true;
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            return false;
        }
    }
}
