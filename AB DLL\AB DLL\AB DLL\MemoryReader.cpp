#include "pch.h"
#include "MemoryReader.h"
#include <iostream>
#include "Map2.h"

// Global variables
HANDLE g_hSharedMemory = nullptr;
PlayerData* g_pSharedData = nullptr;
HANDLE g_hUpdateThread = nullptr;
bool g_bRunning = false;

// Monster scanning globals
bool g_bMonsterScanningActive = false;
HANDLE g_hMonsterScanThread = nullptr;

// Shared memory name for communication with C# app
const wchar_t* SHARED_MEMORY_NAME = L"HethAB_PlayerData";

bool CreateSharedMemory()
{
    // Create shared memory for communication with C# app
    g_hSharedMemory = CreateFileMapping(
        INVALID_HANDLE_VALUE,
        nullptr,
        PAGE_READWRITE,
        0,
        sizeof(PlayerData),
        SHARED_MEMORY_NAME
    );

    if (g_hSharedMemory == nullptr)
    {
        return false;
    }

    g_pSharedData = (PlayerData*)MapViewOfFile(
        g_hSharedMemory,
        FILE_MAP_ALL_ACCESS,
        0,
        0,
        sizeof(PlayerData)
    );

    if (g_pSharedData == nullptr)
    {
        CloseHandle(g_hSharedMemory);
        g_hSharedMemory = nullptr;
        return false;
    }

    // Initialize shared data
    g_pSharedData->x = 0;
    g_pSharedData->y = 0;
    g_pSharedData->isValid = false;
    g_pSharedData->monsterCount = 0;

    return true;
}

void CleanupSharedMemory()
{
    if (g_pSharedData != nullptr)
    {
        UnmapViewOfFile(g_pSharedData);
        g_pSharedData = nullptr;
    }

    if (g_hSharedMemory != nullptr)
    {
        CloseHandle(g_hSharedMemory);
        g_hSharedMemory = nullptr;
    }
}

bool ReadPlayerData()
{
    __try
    {
        // Check if master pointer is valid
        if (Engine::CGame_Character::m_Master == nullptr)
            return false;

        // Read the master pointer value
        uintptr_t masterValue = *Engine::CGame_Character::m_Master;
        if (masterValue == 0)
            return false;

        // Read player coordinates
        int x = *(int*)(masterValue + 18236);
        int y = *(int*)(masterValue + 18244);

        // Update shared memory if available
        if (g_pSharedData != nullptr)
        {
            g_pSharedData->x = x;
            g_pSharedData->y = y;
            g_pSharedData->isValid = true;
        }

        return true;
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        // Memory access failed
        if (g_pSharedData != nullptr)
        {
            g_pSharedData->isValid = false;
        }
        return false;
    }
}
// Map type



bool ScanMonsters()
{
    __try
    {
        if (g_pSharedData == nullptr)
            return false;

        // Reset monster count
        g_pSharedData->monsterCount = 0;

        // Cast the EntityIDs address to our map structure
        typedef std2::map<int, TargetInfo> t_TargetInfoMap;
        t_TargetInfoMap* myMap = reinterpret_cast<t_TargetInfoMap*>((void*)EntityIDs);

        // Validate the map pointer
        if (myMap == nullptr)
            return false;

        // Try to read the map size
        size_t mapSize = 0;
        __try
        {
            mapSize = myMap->size();
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            // Size access failed - memory structure is wrong
            return false;
        }

        // Check if the map has a reasonable size
        if (mapSize == 0)
        {
            // Map is empty - this might be normal if no monsters are nearby
            return true;
        }

        if (mapSize > 1000) // Sanity check - too many entities suggests wrong memory structure
        {
            return false;
        }

        // Try to get iterators
        t_TargetInfoMap::iterator beginIt, endIt;
        __try
        {
            beginIt = myMap->begin();
            endIt = myMap->end();
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            // Iterator access failed
            return false;
        }

        // Iterate through the map
        int monsterIndex = 0;
        for (auto it = beginIt; it != endIt && monsterIndex < 50; ++it)
        {
            __try
            {
                const int key = it.first();
                const TargetInfo& target_ = it.second();

                // Store monster information
                g_pSharedData->monsters[monsterIndex].id = key;
                g_pSharedData->monsters[monsterIndex].address = (int)target_.m_pTarget;

                monsterIndex++;
            }
            __except (EXCEPTION_EXECUTE_HANDLER)
            {
                // Skip this entry if we can't read it
                break;
            }
        }

        // Update final count based on actual iteration
        g_pSharedData->monsterCount = monsterIndex;

        return true;
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        // Memory access failed - reset monster count
        if (g_pSharedData != nullptr)
            g_pSharedData->monsterCount = 0;
        return false;
    }
}

DWORD WINAPI UpdateThread(LPVOID lpParam)
{
    while (g_bRunning)
    {
        ReadPlayerData();
        Sleep(100); // Update every 100ms
    }
    return 0;
}

DWORD WINAPI MonsterScanThread(LPVOID lpParam)
{
    while (g_bMonsterScanningActive)
    {
        ScanMonsters();
        Sleep(500); // Update every 500ms
    }
    return 0;
}

// Exported functions for C# interop
extern "C" {

    ABDLL_API bool InitializeMemoryReader()
    {
        return CreateSharedMemory();
    }

    ABDLL_API void CleanupMemoryReader()
    {
        StopContinuousReading();
        StopMonsterScanning();
        CleanupSharedMemory();
    }

    ABDLL_API bool GetPlayerCoordinates(int* x, int* y)
    {
        if (x == nullptr || y == nullptr)
            return false;

        __try
        {
            // Check if master pointer is valid
            if (Engine::CGame_Character::m_Master == nullptr)
                return false;

            // Read the master pointer value
            uintptr_t masterValue = *Engine::CGame_Character::m_Master;
            if (masterValue == 0)
                return false;

            // Read player coordinates
            *x = *(int*)(masterValue + 18236);
            *y = *(int*)(masterValue + 18244);

            return true;
        }
        __except (EXCEPTION_EXECUTE_HANDLER)
        {
            return false;
        }
    }

    ABDLL_API bool StartContinuousReading()
    {
        if (g_bRunning)
            return true; // Already running

        g_bRunning = true;
        g_hUpdateThread = CreateThread(nullptr, 0, UpdateThread, nullptr, 0, nullptr);
        return g_hUpdateThread != nullptr;
    }

    ABDLL_API void StopContinuousReading()
    {
        g_bRunning = false;
        if (g_hUpdateThread != nullptr)
        {
            WaitForSingleObject(g_hUpdateThread, 5000); // Wait up to 5 seconds
            CloseHandle(g_hUpdateThread);
            g_hUpdateThread = nullptr;
        }
    }

    ABDLL_API bool StartMonsterScanning()
    {
        if (g_bMonsterScanningActive)
            return true; // Already running

        g_bMonsterScanningActive = true;
        g_hMonsterScanThread = CreateThread(nullptr, 0, MonsterScanThread, nullptr, 0, nullptr);
        return g_hMonsterScanThread != nullptr;
    }

    ABDLL_API void StopMonsterScanning()
    {
        g_bMonsterScanningActive = false;
        if (g_hMonsterScanThread != nullptr)
        {
            WaitForSingleObject(g_hMonsterScanThread, 5000); // Wait up to 5 seconds
            CloseHandle(g_hMonsterScanThread);
            g_hMonsterScanThread = nullptr;
        }
    }
}
