using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Heth_AB
{
    public partial class Form1 : Form
    {
        // Process information structure
        private struct ProcessInfo
        {
            public int ProcessId;
            public string ProcessName;
            public override string ToString()
            {
                return $"{ProcessName} (PID: {ProcessId})";
            }
        }

        // Monster information structure
        [StructLayout(LayoutKind.Sequential)]
        private struct MonsterInfo
        {
            public int id;
            public uint address;
        }

        // Shared memory structure matching C++ DLL
        [StructLayout(LayoutKind.Sequential)]
        private struct PlayerData
        {
            public int x;
            public int y;
            public bool isValid;
            public int monsterCount;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 50)]
            public MonsterInfo[] monsters;
        }

        // Win32 API imports for DLL injection
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr OpenProcess(uint dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress, uint dwSize, uint flAllocationType, uint flProtect);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool WriteProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, uint nSize, out UIntPtr lpNumberOfBytesWritten);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr CreateRemoteThread(IntPtr hProcess, IntPtr lpThreadAttributes, uint dwStackSize, IntPtr lpStartAddress, IntPtr lpParameter, uint dwCreationFlags, IntPtr lpThreadId);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr CreateFileMapping(IntPtr hFile, IntPtr lpFileMappingAttributes, uint flProtect, uint dwMaximumSizeHigh, uint dwMaximumSizeLow, string lpName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr MapViewOfFile(IntPtr hFileMappingObject, uint dwDesiredAccess, uint dwFileOffsetHigh, uint dwFileOffsetLow, uint dwNumberOfBytesToMap);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool UnmapViewOfFile(IntPtr lpBaseAddress);

        // DLL function imports
        [DllImport("AB DLL.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern bool StartMonsterScanning();

        [DllImport("AB DLL.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern void StopMonsterScanning();

        // Win32 API for DLL unloading
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

       // [DllImport("kernel32.dll", SetLastError = true)]
       // private static extern IntPtr GetModuleHandle(string lpModuleName);

        // Constants
        private const uint PROCESS_ALL_ACCESS = 0x1F0FFF;
        private const uint MEM_COMMIT = 0x1000;
        private const uint PAGE_READWRITE = 0x04;
        private const uint FILE_MAP_ALL_ACCESS = 0xF001F;

        // Shared memory for communication with DLL
        private IntPtr hSharedMemory = IntPtr.Zero;
        private IntPtr pSharedData = IntPtr.Zero;
        private const string SHARED_MEMORY_NAME = "HethAB_PlayerData";

        private bool isDllInjected = false;
        private int injectedProcessId = 0;

        public Form1()
        {
            InitializeComponent();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            RefreshProcessList();
            InitializeSharedMemory();
        }

        private void RefreshProcessList()
        {
            cmbProcesses.Items.Clear();

            try
            {
                Process[] processes = Process.GetProcessesByName("engine");
                foreach (Process process in processes)
                {
                    ProcessInfo info = new ProcessInfo
                    {
                        ProcessId = process.Id,
                        ProcessName = process.ProcessName
                    };
                    cmbProcesses.Items.Add(info);
                }

                if (cmbProcesses.Items.Count > 0)
                {
                    cmbProcesses.SelectedIndex = 0;
                    lblStatus.Text = $"Found {cmbProcesses.Items.Count} engine.exe process(es)";
                }
                else
                {
                    lblStatus.Text = "No engine.exe processes found";
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error refreshing processes: {ex.Message}";
            }
        }

        private void InitializeSharedMemory()
        {
            try
            {
                hSharedMemory = CreateFileMapping(new IntPtr(-1), IntPtr.Zero, PAGE_READWRITE, 0, (uint)Marshal.SizeOf<PlayerData>(), SHARED_MEMORY_NAME);
                if (hSharedMemory != IntPtr.Zero)
                {
                    pSharedData = MapViewOfFile(hSharedMemory, FILE_MAP_ALL_ACCESS, 0, 0, (uint)Marshal.SizeOf<PlayerData>());
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error initializing shared memory: {ex.Message}";
            }
        }

        private void CleanupSharedMemory()
        {
            if (pSharedData != IntPtr.Zero)
            {
                UnmapViewOfFile(pSharedData);
                pSharedData = IntPtr.Zero;
            }

            if (hSharedMemory != IntPtr.Zero)
            {
                CloseHandle(hSharedMemory);
                hSharedMemory = IntPtr.Zero;
            }
        }

        private bool InjectDLL(int processId)
        {
            try
            {
                // Get the full path to our DLL
                string dllPath = Path.Combine(Application.StartupPath, "AB DLL.dll");
                if (!File.Exists(dllPath))
                {
                    lblStatus.Text = "DLL not found: " + dllPath;
                    return false;
                }

                // Open target process
                IntPtr hProcess = OpenProcess(PROCESS_ALL_ACCESS, false, processId);
                if (hProcess == IntPtr.Zero)
                {
                    lblStatus.Text = "Failed to open target process";
                    return false;
                }

                try
                {
                    // Get LoadLibrary address
                    IntPtr hKernel32 = GetModuleHandle("kernel32.dll");
                    IntPtr pLoadLibrary = GetProcAddress(hKernel32, "LoadLibraryA");

                    if (pLoadLibrary == IntPtr.Zero)
                    {
                        lblStatus.Text = "Failed to get LoadLibrary address";
                        return false;
                    }

                    // Allocate memory in target process
                    byte[] dllPathBytes = Encoding.ASCII.GetBytes(dllPath + "\0");
                    IntPtr pRemoteMemory = VirtualAllocEx(hProcess, IntPtr.Zero, (uint)dllPathBytes.Length, MEM_COMMIT, PAGE_READWRITE);

                    if (pRemoteMemory == IntPtr.Zero)
                    {
                        lblStatus.Text = "Failed to allocate memory in target process";
                        return false;
                    }

                    // Write DLL path to target process
                    UIntPtr bytesWritten;
                    if (!WriteProcessMemory(hProcess, pRemoteMemory, dllPathBytes, (uint)dllPathBytes.Length, out bytesWritten))
                    {
                        lblStatus.Text = "Failed to write DLL path to target process";
                        return false;
                    }

                    // Create remote thread to load DLL
                    IntPtr hThread = CreateRemoteThread(hProcess, IntPtr.Zero, 0, pLoadLibrary, pRemoteMemory, 0, IntPtr.Zero);
                    if (hThread == IntPtr.Zero)
                    {
                        lblStatus.Text = "Failed to create remote thread";
                        return false;
                    }

                    CloseHandle(hThread);
                    lblStatus.Text = "DLL injected successfully";
                    return true;
                }
                finally
                {
                    CloseHandle(hProcess);
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Injection error: {ex.Message}";
                return false;
            }
        }

        private void UpdatePlayerCoordinates()
        {
            if (pSharedData == IntPtr.Zero)
                return;

            try
            {
                PlayerData data = Marshal.PtrToStructure<PlayerData>(pSharedData);
                if (data.isValid)
                {
                    lblXValue.Text = data.x.ToString();
                    lblYValue.Text = data.y.ToString();
                    lblStatus.Text = $"Player coordinates updated. Monsters found: {data.monsterCount}";
                }
                else
                {
                    lblXValue.Text = "N/A";
                    lblYValue.Text = "N/A";
                    lblStatus.Text = "Unable to read player coordinates";
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error reading coordinates: {ex.Message}";
            }
        }

        private void UpdateMonsterList()
        {
            if (pSharedData == IntPtr.Zero)
                return;

            try
            {
                PlayerData data = Marshal.PtrToStructure<PlayerData>(pSharedData);

                // Update monster list
                lstMonsters.Items.Clear();
                for (int i = 0; i < data.monsterCount && i < 50; i++)
                {
                    MonsterInfo monster = data.monsters[i];
                    lstMonsters.Items.Add($"ID: {monster.id}, Address: 0x{monster.address:X8}");
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error updating monster list: {ex.Message}";
            }
        }

        private void btnRefreshProcesses_Click(object sender, EventArgs e)
        {
            RefreshProcessList();
        }

        private void btnInjectDLL_Click(object sender, EventArgs e)
        {
            if (cmbProcesses.SelectedItem == null)
            {
                lblStatus.Text = "Please select a process first";
                return;
            }

            ProcessInfo selectedProcess = (ProcessInfo)cmbProcesses.SelectedItem;

            if (InjectDLL(selectedProcess.ProcessId))
            {
                isDllInjected = true;
                injectedProcessId = selectedProcess.ProcessId;
                timer1.Start();
                btnInjectDLL.Enabled = false;
                lblStatus.Text = "DLL injected, reading coordinates...";
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            if (isDllInjected)
            {
                UpdatePlayerCoordinates();
                UpdateMonsterList();
            }
        }

        private void btnStartMonsterScan_Click(object sender, EventArgs e)
        {
            if (!isDllInjected)
            {
                lblStatus.Text = "Please inject DLL first";
                return;
            }

            try
            {
                if (StartMonsterScanning())
                {
                    btnStartMonsterScan.Enabled = false;
                    btnStopMonsterScan.Enabled = true;
                    lblStatus.Text = "Monster scanning started";
                }
                else
                {
                    lblStatus.Text = "Failed to start monster scanning";
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error starting monster scan: {ex.Message}";
            }
        }

        private void btnStopMonsterScan_Click(object sender, EventArgs e)
        {
            try
            {
                StopMonsterScanning();
                btnStartMonsterScan.Enabled = true;
                btnStopMonsterScan.Enabled = false;
                lstMonsters.Items.Clear();
                lblStatus.Text = "Monster scanning stopped";
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error stopping monster scan: {ex.Message}";
            }
        }

        private void UnloadDLLFromTarget()
        {
            if (!isDllInjected || injectedProcessId == 0)
                return;

            try
            {
                // Open the target process
                IntPtr hProcess = OpenProcess(PROCESS_ALL_ACCESS, false, injectedProcessId);
                if (hProcess == IntPtr.Zero)
                    return;

                try
                {
                    // Get FreeLibrary address
                    IntPtr hKernel32 = GetModuleHandle("kernel32.dll");
                    IntPtr pFreeLibrary = GetProcAddress(hKernel32, "FreeLibrary");

                    if (pFreeLibrary != IntPtr.Zero)
                    {
                        // Get the handle of our DLL in the target process
                        // This is simplified - in practice you'd need to enumerate modules
                        // For now, we'll just signal the DLL to clean up via the shared memory

                        // The DLL will clean up automatically when the process detaches
                        lblStatus.Text = "DLL cleanup initiated";
                    }
                }
                finally
                {
                    CloseHandle(hProcess);
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error during DLL cleanup: {ex.Message}";
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            timer1.Stop();

            // Stop monster scanning if active
            if (isDllInjected)
            {
                try
                {
                    StopMonsterScanning();
                }
                catch { }
            }

            // Unload DLL from target process
            UnloadDLLFromTarget();

            CleanupSharedMemory();
            base.OnFormClosed(e);
        }

        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            timer1.Stop();

            // Stop monster scanning if active
            if (isDllInjected)
            {
                try
                {
                    StopMonsterScanning();
                }
                catch { }
            }

            // Unload DLL from target process
            UnloadDLLFromTarget();

            CleanupSharedMemory();
            //base.OnFormClosed(e);
        }
    }
}
